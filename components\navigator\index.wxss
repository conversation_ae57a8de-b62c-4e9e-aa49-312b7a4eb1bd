/* 自定义导航栏 */
.custom-nav-bar {
  /* position: fixed; */
  /* flex: 1; */
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
}

/* 标题栏 */
.title-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32rpx;
  position: relative;
}

/* 左侧返回按钮区域 */
.nav-left {
  display: flex;
  align-items: center;
  min-width: 120rpx;
}

.back-button {
  display: flex;
  align-items: center;
  /* justify-content: center; */
  position: relative;
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
  /* background-color: rgba(0, 0, 0, 0.1); */
  /* transition: all 0.2s; */
}
.back-icon1, back-icon2{
  height:2rpx;
  width: 50rpx;
  background: #000;
}
.back-button image{
  height: 50rpx;
  width: 50rpx;
  margin-left: -10rpx;
}
.back-button:active {
  background-color: rgba(0, 0, 0, 0.15);
  transform: scale(0.95);
}

.back-icon {
  font-size: 32rpx;
  font-weight: 500;
  color: #000000;
  line-height: 1;
}

/* 标题 */
.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 34rpx;
  font-weight: 500;
  color: #000000;
  max-width: 400rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 右侧胶囊按钮占位 */
.nav-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-width: 120rpx;
}