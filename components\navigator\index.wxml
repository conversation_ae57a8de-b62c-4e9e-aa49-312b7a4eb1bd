<view class="content">
  <view class="custom-nav-bar" style="height: {{navBarHeight}}px; background-color: {{backgroundColor}};">
    <!-- 状态栏占位 -->
    <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

    <!-- 标题栏 -->
    <view class="title-bar" style="height: {{titleBarHeight}}px;">
      <!-- 返回按钮 -->
      <view class="nav-left" wx:if="{{showBack}}">
        <view class="back-button" bindtap="goBack">
          <image  src="/asset/svgs/return.svg"></image>
        </view>
      </view>

      <!-- 标题 -->
      <view class="nav-title" style="color: {{titleColor}};">
        {{title}}
      </view>

      <!-- 右侧胶囊按钮占位 -->
      <view class="nav-right" wx:if="{{showCapsule}}"
            style="width: {{menuButtonInfo.width}}px; height: {{menuButtonInfo.height}}px;">
      </view>
    </view>
  </view>
  <view style="height: calc(100vh - {{navBarHeight}}px)">
    <slot></slot>
  </view>
</view>
