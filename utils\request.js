// 引入全局app实例
const app = getApp();

function request(url, method, data) {
  // 获取存储的 token
  const token = wx.getStorageSync('token');
  
  // 使用app.globalData.baseUrl构建完整URL
  const fullUrl = `${app.globalData.baseUrl}${url}`;
  
  // 设置请求头，如果有 token 则添加 Authorization 头
  const header = {
    'content-type': 'application/json'
  };
  
  if (token) {
    header['Authorization'] = `Bearer ${token}`;
  }
  
  return new Promise((resolve, reject) => {
    wx.request({
      url: fullUrl,
      method: method,
      data: data,
      header: header,
      success: (res) => {
        let data = res.data;
        if (data.code!== 0){
          reject('请求失败')
        }else{
          resolve(res.data);
        }
        
      },
      fail: (err) => {
        reject(err);
      }
    });
  });
}

// 封装 GET 请求
function get(url, data = {}) {
  return request(url, 'GET', data);
}

// 封装 POST 请求
function post(url, data = {}) {
  return request(url, 'POST', data);
}

module.exports = {
  get,
  post
};