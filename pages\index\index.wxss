/**index.wxss**/
.content {
  height: 100vh;
  position: relative;
  overflow: hidden;
}
.canvasImg{
  height: 100vh;
  width: 100vw;
}
.canvas{
  height: 100vh;
  width: 100%;
  position: absolute;
  z-index: -999;
  top: 100vh;
  left: 0;
}
.download{
  position: absolute;
  right: 80rpx;
  bottom: 20rpx;
  height: 60rpx;
  width: auto!important;
  padding: 0 50rpx!important;
  line-height: 60rpx;
  padding: 0;
  display: inline-block;
  color:rgb(255, 255, 255);
  background-color: #26b7f08c;
}

.videoBox, .snapBox,.flexContent{
  /* flex: 1; */
  height: 100%;
  position: relative;
}
.videoBox, .snapBox{
  height: 100%;
}
.snapBox, .snapContent{
  height: 100%;
  width: 100%;
}
.playvideo{
  color:rgb(255, 255, 255);
  background:rgba(104, 245, 247, 0.5);
  text-align: center;
  border-radius: 5px;
  width: 50%;
  margin-left: 25%;
  margin-top: 70vh;
  padding: 10px 0;

}
.videoCover{
  position: absolute;
  height: 100%;
  width: 100vw;
  top: 0;
  left: 0;
  z-index: 1;
}
.videoLogo{
  position: absolute;
  top: 5vw;
  left: 10%;
  height: 150rpx;
  width: 80%;
  z-index: 1;
}
.video{
  height: 100%;
  width: 100vw;
  object-fit: cover;
}
.userId{
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 999;
  font-size: 16rpx;
  color: rgba(0, 0, 0, 0.05);
}
.bk{
  height: 100vh;
  width: 100vw;
  object-fit: contain;
}
.user{
  position: absolute;
  height: 75vh;
  width: 76vw;
  top: 8%;
  left: 12vw;
  object-fit: contain;
}
.open-id{
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  color: red;
}
.open-id image{
  height: 70rpx;
  width: 200rpx;
}
.more{
  position: absolute;
  top: 0rpx;
  right: 0rpx;
  height: 60rpx;
  width: 150rpx;
  color: rgba(255, 253, 151, 1);
  text-align: center;
  background: linear-gradient(90deg, rgba(7, 34, 143, 1) 0%, rgba(174, 88, 233, 1) 35.48%, rgba(14, 62, 181, 1) 73.49%, rgba(104, 245, 247, 1) 100%);
  font-size: 20rpx!important;
  line-height: 60rpx;
  z-index:2;
}
.moreDetails{
  position: absolute;
  top: 60rpx;
  right: 0rpx;
  width: 150rpx;
  background: rgb(255, 255, 255);
  text-align: center;
  z-index:2;
  /* padding: 20rpx 0; */
}
.moreDetails view{
  margin: 10rpx 10rpx;
  padding: 10rpx 0rpx;
  border-radius: 2px;
  
  font-size: 10px;
}
.moreDetails .active{
  background-color: rgba(255, 253, 151, 1);
  box-shadow: 0 0 3px #aaa;
}
.model{
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  z-index: 3;
}
.model .cover{
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: #00000088;
}
.model .inputbox{
  position: absolute;
  left: 10vw;
  top: 25vh;
  height: 50vh;
  width: 80vw;
  display: flex;
  flex-direction: column;
  background: rgb(228, 228, 228);
  border-radius: 20rpx;
}
.model .inputbox .label{
  padding: 30rpx 30rpx 20rpx 30rpx;
}
.model textarea{
  height: auto;
  width: calc(100% - 60rpx);
  margin: 0 30rpx 30rpx  30rpx;
  padding: 20rpx;
  flex:auto;
  box-sizing: border-box;
  background: #fff;
  border-radius: 20rpx;
}
.model .btnbox{
  height: 80rpx;
  display: flex;
  /* width: 400rpx; */
  float: right;
  justify-content: flex-end;
  margin-right: 30rpx;
}
.btnbox button{
  border-radius:  30rpx;
  width: 200rpx;
  height: 60rpx;
  line-height: 30rpx;
  font-size: 20rpx;
  margin: 0 10rpx;
  /* border-radius: 0; */
  
}
.submit{
  background: rgb(196, 196, 196);
}
.errPage{
  position: absolute;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: #fff;
  z-index: 9999;
  display:flex;
  justify-content: center;
  justify-items: center;
  align-items: center;
}









.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.userinfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #aaa;
  width: 80%;
}

.userinfo-avatar {
  overflow: hidden;
  width: 128rpx;
  height: 128rpx;
  margin: 20rpx;
  border-radius: 50%;
}

.usermotto {
  margin-top: 200px;
}

.avatar-wrapper {
  padding: 0;
  width: 56px !important;
  border-radius: 8px;
  margin-top: 40px;
  margin-bottom: 40px;
}

.avatar {
  display: block;
  width: 56px;
  height: 56px;
}

.nickname-wrapper {
  display: flex;
  width: 100%;
  padding: 16px;
  box-sizing: border-box;
  border-top: .5px solid rgba(0, 0, 0, 0.1);
  border-bottom: .5px solid rgba(0, 0, 0, 0.1);
  color: black;
}

.nickname-label {
  width: 105px;
}

.nickname-input {
  flex: 1;
}
