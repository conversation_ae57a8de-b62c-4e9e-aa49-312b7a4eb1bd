// index.js
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
var app = getApp();
let baseUrl = app.globalData.webviewUrl
if (app.globalData.env == 'dev'){
  // baseUrl = 'https://192.168.123.114:5173'
}
//
const http = require('../../utils/request');
Page({
  data: {
    motto: 'Hello World',
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickname: '',
    },
    url: '',
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
    tempTime: 0,
  },
  onLoad(){
    this.openPage()
  },
  onLoad_temp(options) {
    let _tempOption = JSON.stringify(options)
    if (_tempOption === '{}'){

    }else{
      wx.setStorageSync('indexOptions', _tempOption)
    }
    let userid = options.id || options.scene || wx.getStorageSync('userId', userid)
    if (userid) {
      wx.setStorageSync('userId', userid)
    }
    let that = this
    console.log(options)
    // console.log(util.formatTime(new Date()))
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    const token = wx.getStorageSync('token');
    if (token) {
      this.openPage()
      return
    }
    // wx.login({
    //   success (res) {
    //     if (res.code) {
    //       //发起网络请求
    //       http.post('/api/v1.0/user/user_login', {
    //         "platfrom": "wx_mini_program",
    //         "platfrom_app_id": "wx_1111111",
    //         "res_code": res.code
    //       }).then(res => {
    //         let token = res.data.token
    //         console.log('token', token)
    //         console.log('token', res.data)
    //         // 修正：wx.setStorage 需要 key 和 data
    //         wx.setStorageSync('token', token)
    //         console.log('token已保存')
    //         that.openPage()
    //       })
    //     } else {
    //       console.log('登录失败！' + res.errMsg)
    //     }
    //   }
    // })
    // this.fetchData(options);
    
  },
  onShow(options) {
    this.openPage(options)
  },
  onHide(options){
    this.setData({
      url:''
    })
  },
  openPage(options){
    let userId = wx.getStorageSync('userId')
    let token = wx.getStorageSync('token')
    let userInfo = wx.getStorageSync('userInfo')
    let indexOptions = wx.getStorageSync('indexOptions')
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    }
    console.log('userInfo', userInfo)
    // if (!this.data.userInfo.nickname || !token){
    //   wx.redirectTo({
    //     url: '../login/index'
    //   })
    // }else{
    // console.log('userInfo', userInfo)
    let time = new Date().getTime()
    this.global
    let url = `${baseUrl}/?u=${userId}${token?('&c='+token):''}&t=${time}${indexOptions?('&op='+JSON.stringify(indexOptions)):''}`
    console.log('url', url)
    this.setData({
      url: url,
      tempTime: time
    })
    // }
  },
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    const { nickName } = this.data.userInfo
    this.setData({
      "userInfo.avatarUrl": avatarUrl,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },
  onInputChange(e) {
    const nickName = e.detail.value
    const { avatarUrl } = this.data.userInfo
    this.setData({
      "userInfo.nickName": nickName,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },
  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
})
