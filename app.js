// app.js
const accountInfo = wx.getAccountInfoSync();
console.log(accountInfo.miniProgram.envVersion)
let baseUrl = 'https://popofifi.xcastle.net'
let webview = 'https://popofifi.xcastle.net'
if (accountInfo.miniProgram.envVersion == 'develop' || accountInfo.miniProgram.envVersion == 'trial'){
  baseUrl = 'https://popofifi-test.xcastle.net'
  // baseUrl = 'https://popofifi.xcastle.net'
  webview = 'https://popofifi.xcastle.net/wxtest'
}else{
  baseUrl = 'https://popofifi.xcastle.net'
}
App({
  onLaunch() {
    
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },
  globalData: {
    userInfo: null,
    baseUrl: baseUrl,
    webviewUrl: webview,
    env: accountInfo.miniProgram.envVersion == 'develop'?'dev': (accountInfo.miniProgram.envVersion == 'trial'?'test':'prop'),
    webUrl: ""
  }
})
