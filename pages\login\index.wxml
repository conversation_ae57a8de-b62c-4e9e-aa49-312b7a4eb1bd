<!-- <cnavigator class="nav" title="登录" show-back="{{true}}"> -->
  <view class="container">
    <view class="back-icon" bindtap="goBack">
      <image src="/asset/image/loginbk.jpg" mode="aspectFit"></image>
    </view>
    <!-- <view class="title">登录</view> -->
    
    <!-- <view class="cube-container">
      <view class="cube">
        <view class="cube-face front"></view>
        <view class="cube-face back"></view>
        <view class="cube-face right"></view>
        <view class="cube-face left"></view>
        <view class="cube-face top"></view>
        <view class="cube-face bottom"></view>
      </view>
    </view> -->
    
    <!-- 将原来的登录按钮修改为以下代码 -->
    <view class="btnBox">
      <view wx:if="{{!isAgree}}" >
        <button class="wechat-login-btn" bindtap="toAgree" >手机号快捷登录</button>
      </view>
      <view wx:else>
      <button class="wechat-login-btn" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" >手机号快捷登录</button>
      </view>
      <view class="agreement">
        <checkbox checked="{{isAgree}}" bindtap="toggleAgree" color="#4CAF50" />
        <text>我已阅读并同意<text class="link" bindtap="showServiceAgreement">服务协议</text>及<text class="link" bindtap="showPrivacyAgreement">隐私协议</text></text>
      </view>
    </view>
    
  
    

    

    <view class="modal" wx:if="{{!!text}}">
      <scroll-view style="flex: 1;">{{text}}</scroll-view>
      <button bindtap="closeModal">关闭</button>
    </view>
  </view>
<!-- </cnavigator> -->
  