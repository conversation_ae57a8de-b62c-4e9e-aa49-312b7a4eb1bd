// index.js
const defaultAvatarUrl = 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0'
var app = getApp();
let baseUrl = 'https://popofifi.xcastle.net/'
if (app.globalData.env == 'dev'){
  baseUrl = 'https://192.168.123.114:5173/'
}
//
const http = require('../../utils/request');
let nanVideoUrl = 'https://qcard-dev.oss-cn-beijing.aliyuncs.com/prod/00/00/0000000000000000000000000000/wx_mini/nan.mp4?OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Expires=2065792067&Signature=j3Q9Q4nvl%2FL90U5LqyClYSbFAcA%3D'
let nvVideoUrl = 'https://qcard-dev.oss-cn-beijing.aliyuncs.com/prod/00/00/0000000000000000000000000000/wx_mini/nv.mp4?OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Expires=2065792104&Signature=Tx7dypdwBL7V%2FF%2FTtN6kGNFzdS4%3D'

const codes = require('../../utils/code')
// const bodongPic = require('../../asset/image/bodong.png')
Page({
  data: {
    motto: 'Hello World',
    userInfo: {
      avatarUrl: defaultAvatarUrl,
      nickname: '',
    },
    url: '',
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile'),
    canIUseNicknameComp: wx.canIUse('input.type.nickname'),
    tempTime: 0,

    bkImg: '',
    userImg: '',
    lucky_id: '',
    userId: '',
    showMore: false,
    inputValue: '',
    showModel: false,
    isLogin: false,
    isErr: false,
    errorMsg: '无法使用的二维码',
    show3d: false,//app.globalData.env == 'dev' //|| app.globalData.env == 'test'
    showV: false, //显示video
    videoUrl: '',
    isMuted: true,
    canvasImg: '',
    nowStyle: null,
    nowCode: null
  },
  drawCanvas(data){
    console.log(data)
    this.createSelectorQuery()
    .select('#canvas') // 在 WXML 中填入的 id
    .fields({ node: true, size: true })
    .exec((res) => {
        // Canvas 对象
        const canvas = res[0].node
        // 渲染上下文
        const ctx = canvas.getContext('2d')

        // Canvas 画布的实际绘制宽高
        const width = res[0].width
        const height = res[0].height

        // 初始化画布大小
        const dpr = wx.getWindowInfo().pixelRatio
        canvas.width = width * dpr
        canvas.height = height * dpr
        ctx.scale(dpr, dpr)
        console.log(height, width, dpr)

        // 背景图片
        const bkImg = canvas.createImage()
        bkImg.src = data.bkImg
        let promise1 = new Promise((r,j)=>{
          bkImg.onload = e=>{
            ctx.drawImage(bkImg,0,0,width,height)
            
            
            // 先尝试直接路径
            // 用户图片
            const userImg = canvas.createImage()
            userImg.src = data.userImg
            userImg.onload = e=>{
              console.log(userImg)

              // 目标区域中心点（canvas的70%区域的中心）
              const targetCenterX = width * 0.5   // canvas中心X
              const targetCenterY = height * 0.5  // canvas中心Y

              // 计算缩放比例，让图片的最短边占据canvas宽高的70%
              const scaleX = (width * 0.6) / userImg.width
              const scaleY = (height * 0.6) / userImg.height
              const scale = Math.max(scaleX, scaleY) // 使用较大的缩放比例，确保最短边填满70%

              // 计算缩放后的图片尺寸
              const scaledWidth = userImg.width * scale
              const scaledHeight = userImg.height * scale

              // 计算绘制位置（居中，类似overflow: visible）
              const drawX = targetCenterX - scaledWidth / 2
              const drawY = targetCenterY - scaledHeight / 2

              // 绘制完整图片，不裁剪
              ctx.drawImage(userImg, drawX, drawY, scaledWidth, scaledHeight)

              const bodongImg = canvas.createImage()
              bodongImg.onload = e=>{
                console.log('isLoad')
                // 计算bodong图片的尺寸，设置为canvas宽度的20%
                const bodongWidth = width * 0.265
                const bodongHeight = bodongWidth * bodongImg.height / bodongImg.width * 1.7
                // 绘制到左下角，留一些边距
                const margin = 10
                // ctx.drawImage(bodongImg, margin, height - bodongHeight - margin-20, bodongWidth, bodongHeight)

                  // 绘制lucky_id文本
                if (data.lucky_id) {
                  ctx.fillStyle = '#e30101'
                  ctx.font = '16px Arial'
                  ctx.textAlign = 'left'
                  ctx.textBaseline = 'top'

                  const margin = 10

                  // 在bodong图片下方绘制文本
                  const textX = margin
                  const textY = height - margin -15 // bodong图片下方5px处
                  ctx.fillText(`ID: ${data.lucky_id}`, 60, drawY - 10)
                  ctx.fillText(`战队: ${this.data.nowCode.name}战队`, 60, drawY + 10)
                }
                r('ok')
              }
              bodongImg.src = '/asset/image/bodong.png'
            }
            
          }
        })

        


        Promise.all([promise1]).then(e=>{
          console.log('finnish')
          console.log(e)

        

          wx.canvasToTempFilePath({
            canvas,
            success: res => {
                // 生成的图片临时文件路径
                const tempFilePath = res.tempFilePath
                this.setData({
                  canvasImg: tempFilePath
                })
            },
          })
        })
    })
  },
  snap(){
        // 下载成功后将图片保存到本地
        wx.saveImageToPhotosAlbum({
          filePath: this.data.canvasImg,
          success: function() {
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 2000
            });
          },
          fail: function(err) {
            console.log(err)
            wx.showToast({
              title: JSON.stringify(err)|| '保存失败',
              icon: 'none',
              duration: 2000
            });
          }
        });
    this.createSelectorQuery()
      .select("#snap")
      .node()
      .exec(res => {
        console.log(res)
        const node = res[0].node
        node.takeSnapshot({
          type: 'arraybuffer', //'arraybuffer',
          format: 'png',
          success: (res) => {
            const f = `${wx.env.USER_DATA_PATH}/POPOFIFI.png`
            const fs = wx.getFileSystemManager();
            fs.writeFileSync(f, res.data, 'binary')
            

            wx.saveImageToPhotosAlbum({
              filePath: f,
              success(res) {
                wx.showToast({
                  title: '保存成功'
                })
                console.log("saveImageToPhotosAlbum:", res)
              },
              fail(){
                wx.showToast({
                  title: '未保存成功'
                })
              }
            })
          },
          fail(res) {
            console.log("takeSnapshot fail:", res)
          }
        })
      })
  },
  openMusic(){this.setData({isMuted: false})},
  showMore(){
    console.log(this.data.showMore)
    this.setData({
      showMore: !this.data.showMore
    })
  },
  to2d(){
    this.setData({showV: false})
  },
  toVideo(){
    this.setData({showV: true})
  },
  to3d(){
    let state = this.toLogin()
    if (state) return
    wx.redirectTo({
      url: '/pages/avartar/index',
    });
  },
  jianyi(){
    let state = this.toLogin()
    if (state) return
    this.setData({
      showModel: true
    })
  },
  remarkInputAction(options){
    let value = options.detail.value;
    this.setData({
      inputValue: value
    })
  },
  submit(){
    http.post('/api/v1.0/user/feedback',{
      content: this.data.inputValue,
    }).then(res => {
      wx.showToast({
        title: '提交成功',
        icon: 'success',
        duration: 2000
      });
      this.setData({
        showModel: false,
        inputValue: ''
      })
    })
    console.log(this.data.inputValue)
  },
  download(){
    console.log(wx.env.USER_DATA_PATH)
    wx.downloadFile({
      url: this.data.userImg,
      filePath: wx.env.USER_DATA_PATH+'/您的POPOFIFI形象.png',
      success: function(res) {
        console.log(res)
        // 下载成功后将图片保存到本地
        wx.saveImageToPhotosAlbum({
          filePath: res.filePath,
          success: function() {
            wx.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 2000
            });
          },
          fail: function(err) {
            console.log(err)
            wx.showToast({
              title: JSON.stringify(err)|| '保存失败',
              icon: 'none',
              duration: 2000
            });
          }
        });
      },
      fail: function(err) {
        console.log(err)
        wx.showToast({
          title: JSON.stringify(err)||'下载失败',
          icon: 'none',
          duration: 2000

        });
      }
    });
  },
  closeModel(){
    this.setData({
      showModel: false
    })
    console.log(this.data.inputValue)
  },
  toLogin(){
    if (!this.data.isLogin){
      console.log('not Login')
      wx.redirectTo({
        url: '/pages/login/index',
        fail(e){
          console.log(e)
        }
      })
      return true
    }
    
  },
  onLoad(options) {
    // 更新相关 
    const updateManager = wx.getUpdateManager()

    updateManager.onCheckForUpdate(function (res) {
      // 请求完新版本信息的回调
      console.log(res.hasUpdate)
    })

    updateManager.onUpdateReady(function () {
      wx.showModal({
        title: '更新提示',
        content: '新版本已经准备好，是否重启应用？',
        success(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate()
          }
        }
      })
    })

    updateManager.onUpdateFailed(function () {
      // 新版本下载失败
    })

    let _tempOption = JSON.stringify(options)
    if (_tempOption === '{}'){

    }else{
      wx.setStorageSync('indexOptions', _tempOption)
    }

    let defaultId = '6C0FB9D8A9BB2918357F28885EE09166' //正式
    if (app.globalData.env === 'test' || app.globalData.env === 'dev') defaultId = '9DFF77B258CAE1D29BEA90C1E5917A67'
    // 后端传输的大概率是scene
    let userid = options.id|| options.scene || wx.getStorageSync('userId', userid) || defaultId
    if (userid) {
      wx.setStorageSync('userId', userid)
    }
    this.setData({
      userId: userid
    })
    let that = this
    console.log(options)
    // console.log(util.formatTime(new Date()))
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    const token = wx.getStorageSync('token');
    // this.openPage()
    http.get('/api/v1.0/user/get_user_info').then(e=>{
      console.log(e)
    }).catch(err=>{
      console.log(err)
      this.setData({
        token: '',
        isLogin: false
      })
      wx.setStorageSync('token', '')
      
    })
    // wx.login({
    //   success (res) {
    //     if (res.code) {
    //       //发起网络请求
    //       http.post('/api/v1.0/user/user_login', {
    //         "platfrom": "wx_mini_program",
    //         "platfrom_app_id": "wx_1111111",
    //         "res_code": res.code
    //       }).then(res => {
    //         let token = res.data.token
    //         console.log('token', token)
    //         console.log('token', res.data)
    //         // 修正：wx.setStorage 需要 key 和 data
    //         wx.setStorageSync('token', token)
    //         console.log('token已保存')
    //         that.openPage()
    //       })
    //     } else {
    //       console.log('登录失败！' + res.errMsg)
    //     }
    //   }
    // })
    // this.fetchData(options);
    
  },
  onShow(options) {
    this.openPage(options)
  },
  onHide(options){
  },
  openPage(options){
    let userId = wx.getStorageSync('userId')
    let token = wx.getStorageSync('token')
    let userInfo = wx.getStorageSync('userInfo')
    let indexOptions = wx.getStorageSync('indexOptions')
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    }
    if (token){
      this.setData({
        isLogin:true
      })
    }
    console.log('userInfo', userInfo)
    let styles = http.get('/api/v1.0/user/get_def_style_cfg')
    let avatarInfo = http.get('/api/v1.0/user/get_digital_avatar_info', {
      digital_avatar_id: userId
    }).catch(e=>{
      console.log(e)
      this.setData({
        isErr: true
      })
    })
    Promise.all([styles, avatarInfo]).then(res=>{
      let styleLst = res[0].data
      let e = res[1]
      console.log(e.data.scene_bkg)
      console.log(e.data.avatar['2d'])
      if (e.data.style_comm == 'soldier')e.data.style_comm = 'xuan'
      let code = codes.codeLst.find(_e=>_e.style == e.data.style_comm)
      code = code || codes.codeLst[0]
      let bk = ''
      // if (!code){
      //   code = codes.codeLst[Math.floor(Math.random() * 9)]
      //   code = codes.codeLst[0]
      //   bk = e.data.scene_bkg
      // }else{
      //   bk = `/asset/bgs/${code.img}`
      // }
      // 005 solder
      // id: '113', style: 'xuan', name: '炫光光',img: '2.jpg',
      
      let hasNewStyle = styleLst.find(_e=>_e.code == e.data.style_comm)
      if (hasNewStyle){
        this.setData({
          nowStyle: hasNewStyle,
          nowCode: code
        })
        bk = hasNewStyle.mp_bkg
      }else{
        bk = styleLst[0].mp_bkg
      }
      bk = bk || e.data.scene_bkg
      bk = e.data.scene_bkg
      console.log('sssss')
      // isNv = true
      this.setData({
        bkImg: bk,
        userImg: e.data.avatar['2d'],
        lucky_id: e.data.lucky_id,
        videoUrl: code.url
      })
      this.drawCanvas({
        bkImg: bk,
        userImg: e.data.avatar['2d'],
        lucky_id: e.data.lucky_id,
      })
    })
    // if (!this.data.userInfo.nickname || !token){
    //   wx.redirectTo({
    //     url: '../login/index'
    //   })
    // }else{
    // console.log('userInfo', userInfo)
    // let time = new Date().getTime()
    // this.global
    // let url = `${baseUrl}?u=${userId}${token?('&c='+token):''}&t=${time}${indexOptions?('&op='+JSON.stringify(indexOptions)):''}`
    // console.log('url', url)
    // this.setData({
    //   url: url,
    //   tempTime: time
    // })
    // }
  },
  bindViewTap() {
    wx.navigateTo({
      url: '../logs/logs'
    })
  },
  onChooseAvatar(e) {
    const { avatarUrl } = e.detail
    const { nickName } = this.data.userInfo
    this.setData({
      "userInfo.avatarUrl": avatarUrl,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },
  onInputChange(e) {
    const nickName = e.detail.value
    const { avatarUrl } = this.data.userInfo
    this.setData({
      "userInfo.nickName": nickName,
      hasUserInfo: nickName && avatarUrl && avatarUrl !== defaultAvatarUrl,
    })
  },
  getUserProfile(e) {
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认，开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '展示用户信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res)
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
})
