const http = require('../../utils/request');
Page({
  data: {
    isAgree: false,
    text: ''
  },
  handleAgreePrivacyAuthorization:function(e){
    console.log(e)
  },
  closeModal: function (){
    this.setData({
      text: ''
    })
  },
  openModal: function(value){
    let text = ''
    if (value == 'fuwu'){
      text = 'fuwu'
    }
    if (value == 'yinsi'){
      text = 'yinsi'
    }
    this.setData({
      text: text
    })
  },
  // onLoad: function(options) {
  //   console.log(options)
  //   // 页面加载时的逻辑
  // },

  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },
  toAgree: function() {
      wx.showToast({
        title: '请先同意服务协议和隐私协议',
        icon: 'none'
      });
  },
  // 修改为处理获取手机号后的回调
  getPhoneNumber: function(e) {
    
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 用户同意授权，获取到加密数据
      const encryptedData = e.detail.encryptedData;
      const iv = e.detail.iv;
      const code = e.detail.code; // 基础库 2.21.2 及以上版本可以直接获取到 code
      
      // 这里可以将获取到的数据发送到后端进行解密处理
      console.log('获取手机号密码', encryptedData, iv, code);

      //发起网络请求
      http.post('/api/v1.0/user/update_user_mobile', {
        data:encryptedData,iv,code
      }).then(res => {
        // 获取手机号成功后跳转
        wx.showToast({
          title: '登录成功',
        })
        setTimeout(e=>{
          wx.redirectTo({
            url: '/pages/index/index',
          });
        },1500)
        
      }).catch(err => {
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'error',
          duration: 2000
        })
      })

      
    } else {
      // 用户拒绝授权
      console.log('用户拒绝授权手机号', e.detail.errMsg);
      wx.showToast({
        title: '需要授权手机号才能继续',
        icon: 'none'
      });
    }
  },
  onLoad(options) {
    console.log(options)
    // console.log(util.formatTime(new Date()))
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
    // const token = wx.getStorageSync('token');
    // if (token) {
    //   this.getUserData()
    //   return
    // }
    wx.login({
      success (res) {
        if (res.code) {
          //发起网络请求
          http.post('/api/v1.0/user/user_login', {
            "platfrom": "wx_mini_program",
            "platfrom_app_id": "wx_1111111",
            "res_code": res.code
          }).then(res => {
            let token = res.data.token
            console.log('token', token)
            console.log('token', res.data)
            // 修正：wx.setStorage 需要 key 和 data
            wx.setStorageSync('token', token)
            console.log('token已保存')
          })
        } else {
          console.log('登录失败！' + res.errMsg)
        }
      }
    })
    // this.fetchData(options);
    
  },
  toggleAgree: function() {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  showServiceAgreement: function() {
    // this.openModal('fuwu')
    this.previewFile('https://popofifi.xcastle.net/minigram/fuwu.docx')
  },

  showPrivacyAgreement: function() {
    // this.openModal('yinsi')
    this.previewFile('https://popofifi.xcastle.net/minigram/yinsi.docx')
  },

  // 预览文件
  previewFile(fileLink) {
    let that = this
    if(!fileLink) {
        return false
    }
    this.showLoading()
  
    // 单次下载允许的最大文件为 200MB
    wx.downloadFile({
        url: fileLink, // 地址已打码，自己换个其他的地址（"https://www.xxxxx.com/file/测试通知.pdf"）
        success: function (res) {
            console.log(res, "wx.downloadFile success res")
            if(res.statusCode != 200) {
                that.hideLoadingWithErrorTips()
                return false
            }
            that.hideLoading()
            var Path = res.tempFilePath //返回的文件临时地址，用于后面打开本地预览所用
            wx.openDocument({
                filePath: Path,
                showMenu: true,
                success: function (res) {
                    console.log('打开成功');
                    that.hideLoading()
                }
            })
        },
        fail: function (err) {
            console.log(err, "wx.downloadFile fail err");
            that.hideLoading()
            that.hideLoadingWithErrorTips()
        }
    })
  
  },
  showLoading:function (tips = '加载中...') {
    wx.showNavigationBarLoading()
    wx.showLoading({
      title: tips,
    })
  },
   
  hideLoading:function () {
    wx.hideLoading()
    wx.hideNavigationBarLoading()
  },
   
  hideLoadingWithErrorTips:function (err = '加载失败...') {
    hideLoading()
    wx.showToast({
      title: err,
      icon: 'error',
      duration: 2000
    })
  }
});