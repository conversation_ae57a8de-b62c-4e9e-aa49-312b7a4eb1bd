.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;
  background-color: #000;
  position: relative;
  padding: 0 30rpx;
}
.btnBox{
  /* margin-bottom: 200rpx; */
  flex: 2;
}
.agreement{
  margin-top: 100rpx;
}
.back-icon {
  /* position: absolute; */

  width: 70%;
  height: 500rpx;
  left: 0rpx;
  z-index: 10;
  flex: 3;
}

.back-icon image {
  width: 100%;
  height: 100%;
}

.title {
  color: white;
  font-size: 36rpx;
  margin-top: 100rpx;
  margin-bottom: 100rpx;
}

.cube-container {
  width: 300rpx;
  height: 300rpx;
  perspective: 1000rpx;
  margin: 100rpx 0;
}

.cube {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transform: rotateX(-15deg) rotateY(45deg);
  animation: rotate 20s infinite linear;
}

.cube-face {
  position: absolute;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 0.8;
}

@keyframes rotate {
  0% {
    transform: rotateX(-15deg) rotateY(0deg);
  }
  100% {
    transform: rotateX(-15deg) rotateY(360deg);
  }
}

.wechat-login-btn {
  width: 450rpx!important;
  /* height: 90rpx; */
  line-height: 40rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 45rpx;
  /* margin-top: 100rpx; */
  /* font-size: 32rpx; */
  font-weight: 300;
}

.agreement {
  display: flex;
  align-items: center;
  /* margin-bottom: 300rpx;
  margin-top: -150px; */
  font-size: 24rpx;
  color: #999;
}
.agreement checkbox{
  border-radius: 100%;
}
.link {
  color: #4CAF50;
}

checkbox .wx-checkbox-input {
	width: 32rpx; 
  height: 32rpx;
  color: #fff;
  /* border-color: #fff; */
  border-radius: 100%;
  background-color: #4CAF50;
}
checkbox .wx-checkbox-input-checked::before {
	color: #fff; /* 这里也可以设置对钩的颜色 */
    /* background-color:  #4CAF50; */
}

.modal{
  position: absolute;
  background: #fff;
  width: 80%;
  height: 80%;
  padding: 20rpx;
  border-radius: 10px;
  top: 10%;
  z-index: 55;
  display: flex;
  flex-direction: column;
}

.model button{
  height: 50px;
  flex: 1;
}