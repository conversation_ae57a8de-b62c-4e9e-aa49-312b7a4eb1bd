// components/navigator.js
Component({

  /**
   * 组件的属性列表
   */
  properties: {
    // 导航栏标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示返回按钮
    showBack: {
      type: Boolean,
      value: true
    },
    // 导航栏背景色
    backgroundColor: {
      type: String,
      value: '#ffffff'
    },
    // 标题颜色
    titleColor: {
      type: String,
      value: '#000000'
    },
    // 是否显示胶囊按钮占位
    showCapsule: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: 0,
    titleBarHeight: 44,
    menuButtonInfo: {},
    navBarHeight: 0
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.setNavBarInfo()
    }
  },

  /**
   * 页面生命周期
   */
  pageLifetimes: {
    show() {
      this.setNavBarInfo()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 设置导航栏信息
    setNavBarInfo() {
      try {
        const systemInfo = wx.getSystemInfoSync()
        const menuButtonInfo = wx.getMenuButtonBoundingClientRect()

        const statusBarHeight = systemInfo.statusBarHeight || 20
        const titleBarHeight = menuButtonInfo.height + (menuButtonInfo.top - statusBarHeight) * 2
        const navBarHeight = statusBarHeight + titleBarHeight

        this.setData({
          statusBarHeight,
          titleBarHeight,
          menuButtonInfo,
          navBarHeight
        })

        // 触发事件，通知父组件导航栏高度
        this.triggerEvent('navheight', {
          navBarHeight: navBarHeight,
          statusBarHeight: statusBarHeight,
          titleBarHeight: titleBarHeight
        })

        // 设置全局CSS变量（如果支持的话）
        if (typeof wx.setNavigationBarColor === 'function') {
          const app = getApp()
          if (app.globalData) {
            app.globalData.navBarHeight = navBarHeight
          }
        }
      } catch (error) {
        console.error('设置导航栏信息失败:', error)
        // 使用默认值
        this.setData({
          statusBarHeight: 20,
          titleBarHeight: 44,
          navBarHeight: 64
        })
      }
    },

    // 返回上一页
    goBack() {
      const pages = getCurrentPages()
      if (pages.length > 1) {
        wx.navigateBack()
      } else {
        wx.redirectTo({
          url: '/pages/index/index'
        })
      }
    }
  }
})