
<!-- <cnavigator class="nav" title="POPOFIFI" show-back="{{false}}"> -->
  <view class="content">
    <!-- <snapshot class="snapBox" id="snap" wx:if="{{!showV}}"> -->
      <canvas  type="2d" id="canvas" class="canvas"></canvas>
    <view class="snapBox" id="snap" wx:if="{{!showV}}">
      
      <image src="{{canvasImg}}" class="canvasImg"></image>
      <view  class="snapContent">
        
        <!-- <image class="bk" src="{{bkImg}}" ></image>
        <image class="user" src="{{userImg}}" mode="aspectFit"></image>
        <view class="open-id">
          <image src="/asset/image/bodong.png" />
          <view >ID:{{ lucky_id }}</view>
        </view> -->
      </view>
    </view>
    <!-- </snapshot> -->
    <button bind:tap="snap"  wx:if="{{!showV}}" class="download">下载图片</button>
    <view class="videoBox" wx:if="{{showV}}">
      <image class="videoLogo" src="/asset/image/videoLogo.png" mode="aspectFit"></image>
      <view class="videoCover" wx:if="{{isMuted}}" bind:tap="openMusic">
        <view class="playvideo">点击加入战队</view>
      </view>
      <!-- <video class="video" autoplay  loop muted="muted" src="https://qcard-dev.oss-cn-beijing.aliyuncs.com/prod/00/00/0000000000000000000000000000/wx_mini/nan.mp4?OSSAccessKeyId=LTAI5tBQ9LZEbf3qinkTq5oa&Expires=2065792067&Signature=j3Q9Q4nvl%2FL90U5LqyClYSbFAcA%3D" 	object-fit="cover" 
      ></video> -->
      <video class="video" autoplay  loop muted="{{isMuted}}" src="{{videoUrl}}" 	object-fit="cover" 
        show-mute-btn="true"
        show-bottom-progress="{{false}}"
        controls="{{false}}"
        enable-progress-gesture="{{false}}"
      ></video>
    </view>
    <view class="open-id" wx:if="{{showV}}">
      <image src="/asset/image/bodong.png" />
      <view>ID:{{ lucky_id }}</view>
    </view>
    <view class="more" bind:tap="showMore">
      ●  ●  ●
    </view>
    <view class="moreDetails" wx:if="{{showMore}}" bind:tap="showMore">
      <view bind:tap="to2d" class="{{showV?'':'active'}}">我的通行证</view>
      <view bind:tap="toVideo" class="{{showV?'active':''}}">召唤队长</view>
      <view bind:tap="to3d" wx:if="{{show3d}}">3d</view>
      <!-- <view bind:tap="download" >下载照片</view> -->
      <view bind:tap="jianyi">问题反馈</view>
    </view>
    <view class="model" wx:if="{{showModel}}">
      <view class="cover"></view>
      <view class="inputbox">
        <view class="label">感谢您的反馈</view>
        <textarea bindinput="remarkInputAction" placeholder="请输入您的反馈"></textarea>
        <view class="btnbox">
          <button bind:tap="closeModel">关闭</button>
          <button bind:tap="submit" class="submit">提交</button>
          
        </view>
        
      </view>
    </view>
    <view class="errPage" wx:if="{{isErr}}">
      <view class="errorMsg">{{errorMsg}}</view>
    </view>
    <view class="userId">{{userId}} </view>
    
  </view>
<!-- </cnavigator> -->